from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import tempfile
from extract_tariff import extract_tariff_from_pdf
import traceback
import json
from datetime import datetime
import uuid

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# In-memory storage for tariff data (replace with database in production)
tariff_storage = {}

@app.route('/api/extract-tariff', methods=['POST'])
def extract_tariff():
    """
    API endpoint to extract tariff data from uploaded PDF file.
    Expects a PDF file in the request and returns extracted tariff data.
    """
    try:
        # Check if file is present in request
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Check if file is PDF
        if not file.filename.lower().endswith('.pdf'):
            return jsonify({
                'success': False,
                'error': 'Only PDF files are supported'
            }), 400
        
        # Get optional parameters
        use_llm = request.form.get('use_llm', 'true').lower() == 'true'
        
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            # Extract tariff data using the extract_tariff.py function
            extracted_data = extract_tariff_from_pdf(
                pdf_path=temp_path,
                use_llm=use_llm
            )
            
            # Clean up temporary file
            os.unlink(temp_path)
            
            if not extracted_data:
                return jsonify({
                    'success': False,
                    'error': 'No tariff data could be extracted from the PDF'
                }), 400
            
            return jsonify({
                'success': True,
                'data': extracted_data,
                'count': len(extracted_data)
            })
            
        except Exception as e:
            # Clean up temporary file in case of error
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise e
            
    except Exception as e:
        print(f"Error extracting tariff: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'Error processing PDF: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'Tariff extraction service is running'
    })

# Tariff Management Endpoints

@app.route('/admin/hotel/<hotel_id>/tariffs', methods=['GET'])
def get_hotel_tariffs(hotel_id):
    """Get all tariff uploads for a specific hotel"""
    try:
        hotel_tariffs = []
        for tariff_id, tariff_data in tariff_storage.items():
            if tariff_data.get('hotelId') == hotel_id:
                hotel_tariffs.append(tariff_data)

        return jsonify({
            'success': True,
            'result': hotel_tariffs
        })
    except Exception as e:
        print(f"Error fetching hotel tariffs: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error fetching tariffs: {str(e)}'
        }), 500

@app.route('/admin/hotel/tariff', methods=['POST'])
def create_tariff_upload():
    """Create a new tariff upload"""
    try:
        data = request.get_json()

        # Generate a unique tariff ID
        tariff_id = str(uuid.uuid4())

        # Create tariff object
        tariff = {
            'tariffId': tariff_id,
            'hotelId': data.get('hotelId'),
            'roomId': data.get('roomId'),
            'filePath': data.get('filePath'),
            'uploadDate': datetime.now().isoformat(),
            'status': 'pending'
        }

        # Store in memory
        tariff_storage[tariff_id] = tariff

        return jsonify({
            'success': True,
            'result': tariff
        })
    except Exception as e:
        print(f"Error creating tariff upload: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error creating tariff: {str(e)}'
        }), 500

@app.route('/admin/hotel/tariff/<tariff_id>', methods=['PUT'])
def update_tariff_status(tariff_id):
    """Update tariff status (approve/reject)"""
    try:
        data = request.get_json()

        # Check if tariff exists
        if tariff_id not in tariff_storage:
            return jsonify({
                'success': False,
                'error': 'Tariff not found'
            }), 404

        # Update tariff data
        tariff = tariff_storage[tariff_id]
        tariff['status'] = data.get('status')
        tariff['priceData'] = data.get('priceData')
        tariff['notes'] = data.get('notes')
        tariff['approvalDate'] = datetime.now().isoformat()
        tariff['approvedBy'] = 'Admin User'  # In production, get from auth context

        return jsonify({
            'success': True,
            'result': tariff
        })
    except Exception as e:
        print(f"Error updating tariff status: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error updating tariff: {str(e)}'
        }), 500

@app.route('/admin/hotel/tariff/<tariff_id>', methods=['DELETE'])
def delete_tariff_upload(tariff_id):
    """Delete a tariff upload"""
    try:
        # Check if tariff exists
        if tariff_id not in tariff_storage:
            return jsonify({
                'success': False,
                'error': 'Tariff not found'
            }), 404

        # Delete tariff
        del tariff_storage[tariff_id]

        return jsonify({
            'success': True,
            'result': {'success': True}
        })
    except Exception as e:
        print(f"Error deleting tariff: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error deleting tariff: {str(e)}'
        }), 500

if __name__ == '__main__':
    # Create output directory if it doesn't exist
    os.makedirs('output', exist_ok=True)
    
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)